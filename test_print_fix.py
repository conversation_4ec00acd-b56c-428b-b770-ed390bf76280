#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات الطباعة - A4 بحجم كامل
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.users import init_db
from utils.new_design_invoice_printer import NewDesignInvoicePrintDialog
from utils.quarter_invoice_printer import NewInvoicePrintDialog
from utils.advanced_invoice_printer import AdvancedInvoicePrintDialog

class TestPrintWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار تحسينات الطباعة - A4 بحجم كامل")
        self.setGeometry(100, 100, 400, 300)

        # إعداد قاعدة البيانات
        self.setup_database()

        # إعداد الواجهة
        self.setup_ui()

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            # مسار قاعدة البيانات
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounting.db')

            # إنشاء محرك قاعدة البيانات
            self.engine = create_engine(
                f'sqlite:///{db_path}',
                echo=False,
                pool_pre_ping=True,
                pool_recycle=300,
                connect_args={'check_same_thread': False}
            )

            # إنشاء جلسة قاعدة البيانات
            self.Session = scoped_session(sessionmaker(bind=self.engine))

            # التأكد من وجود الجداول
            if not os.path.exists(db_path):
                init_db(self.engine)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إعداد قاعدة البيانات:\n{str(e)}")
            sys.exit(1)
        
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # عنوان
        title_style = """
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2C3E50;
                text-align: center;
                padding: 20px;
                background-color: #ECF0F1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """
        
        # أزرار الاختبار
        btn_style = """
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                padding: 15px;
                margin: 5px;
                border: none;
                border-radius: 8px;
                color: white;
            }
            QPushButton:hover {
                opacity: 0.8;
            }
        """
        
        # زر اختبار التصميم الجديد
        new_design_btn = QPushButton("🎨 اختبار التصميم الجديد (New Design)")
        new_design_btn.setStyleSheet(btn_style + "background-color: #3498DB;")
        new_design_btn.clicked.connect(self.test_new_design)
        layout.addWidget(new_design_btn)
        
        # زر اختبار تصميم الأرباع
        quarter_btn = QPushButton("📊 اختبار تصميم الأرباع (Quarter)")
        quarter_btn.setStyleSheet(btn_style + "background-color: #E74C3C;")
        quarter_btn.clicked.connect(self.test_quarter)
        layout.addWidget(quarter_btn)
        
        # زر اختبار التصميم المتقدم
        advanced_btn = QPushButton("⚡ اختبار التصميم المتقدم (Advanced)")
        advanced_btn.setStyleSheet(btn_style + "background-color: #27AE60;")
        advanced_btn.clicked.connect(self.test_advanced)
        layout.addWidget(advanced_btn)
        
        # زر إنشاء فاتورة تجريبية
        create_btn = QPushButton("➕ إنشاء فاتورة تجريبية")
        create_btn.setStyleSheet(btn_style + "background-color: #F39C12;")
        create_btn.clicked.connect(self.create_test_invoice)
        layout.addWidget(create_btn)
        
    def get_latest_invoice_id(self):
        """الحصول على آخر فاتورة"""
        try:
            session = self.Session()
            from database.models import Invoice
            latest_invoice = session.query(Invoice).order_by(Invoice.id.desc()).first()
            session.close()

            if latest_invoice:
                return latest_invoice.id
            else:
                QMessageBox.warning(self, "تحذير", "لا توجد فواتير في قاعدة البيانات")
                return None
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في الوصول لقاعدة البيانات:\n{str(e)}")
            return None
    
    def test_new_design(self):
        """اختبار التصميم الجديد"""
        invoice_id = self.get_latest_invoice_id()
        if invoice_id:
            dialog = NewDesignInvoicePrintDialog(self.engine, invoice_id, self)
            dialog.exec_()

    def test_quarter(self):
        """اختبار تصميم الأرباع"""
        invoice_id = self.get_latest_invoice_id()
        if invoice_id:
            dialog = NewInvoicePrintDialog(self.engine, invoice_id, self)
            dialog.exec_()

    def test_advanced(self):
        """اختبار التصميم المتقدم"""
        invoice_id = self.get_latest_invoice_id()
        if invoice_id:
            dialog = AdvancedInvoicePrintDialog(self.engine, invoice_id, self)
            dialog.exec_()
    
    def create_test_invoice(self):
        """إنشاء فاتورة تجريبية للاختبار"""
        try:
            session = self.Session()
            from database.models import Invoice, InvoiceItem, Customer, Product
            from datetime import datetime
            
            # إنشاء عميل تجريبي
            customer = Customer(
                name="عميل تجريبي للطباعة",
                phone="01234567890",
                address="عنوان تجريبي للاختبار"
            )
            session.add(customer)
            session.flush()
            
            # إنشاء فاتورة تجريبية
            invoice = Invoice(
                customer_id=customer.id,
                date=datetime.now(),
                total_amount=1500.0,
                discount=50.0,
                final_amount=1450.0,
                payment_method="نقدي",
                notes="فاتورة تجريبية لاختبار تحسينات الطباعة"
            )
            session.add(invoice)
            session.flush()
            
            # إضافة منتجات تجريبية
            products_data = [
                {"name": "منتج تجريبي 1", "price": 500.0, "quantity": 2},
                {"name": "منتج تجريبي 2", "price": 300.0, "quantity": 1},
                {"name": "منتج تجريبي 3", "price": 200.0, "quantity": 1}
            ]
            
            for product_data in products_data:
                # إنشاء المنتج
                product = Product(
                    name=product_data["name"],
                    price=product_data["price"],
                    category="تجريبي"
                )
                session.add(product)
                session.flush()
                
                # إضافة المنتج للفاتورة
                invoice_item = InvoiceItem(
                    invoice_id=invoice.id,
                    product_id=product.id,
                    quantity=product_data["quantity"],
                    unit_price=product_data["price"],
                    total_price=product_data["price"] * product_data["quantity"]
                )
                session.add(invoice_item)
            
            session.commit()
            session.close()
            
            QMessageBox.information(
                self, 
                "نجح", 
                f"تم إنشاء فاتورة تجريبية برقم {invoice.id:06d}\n"
                "يمكنك الآن اختبار الطباعة باستخدام الأزرار أعلاه"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء الفاتورة التجريبية:\n{str(e)}")

def main():
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = TestPrintWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
